"""
缓存服务模块
负责管理 CVE 总结的缓存，避免重复调用 LLM
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from sqlalchemy.orm import selectinload

from ..core.database import CVESummaryCache, SearchLog
from ..core.config import settings
from ..models.schemas import CVESummary, CVEInfo
from .llm_service import LLMService


class CacheService:
    """缓存服务类"""
    
    def __init__(self):
        self.llm_service = LLMService()
        self.cache_ttl = settings.cache.ttl
    
    async def get_or_generate_summary(
        self, 
        cve_info: CVEInfo, 
        db: AsyncSession,
        force_refresh: bool = False
    ) -> Optional[CVESummary]:
        """
        获取或生成 CVE 总结
        
        Args:
            cve_info: CVE 信息
            db: 数据库会话
            force_refresh: 是否强制刷新缓存
            
        Returns:
            CVESummary: CVE 总结，如果失败返回 None
        """
        # 如果不强制刷新，先尝试从缓存获取
        if not force_refresh:
            cached_summary = await self._get_cached_summary(cve_info.cve_id, db)
            if cached_summary:
                print(f"从缓存获取 CVE {cve_info.cve_id} 的总结")
                return cached_summary
        
        # 检查 LLM 服务是否配置
        if not self.llm_service.is_configured():
            print("LLM 服务未正确配置，跳过总结生成")
            return None
        
        # 生成新的总结
        print(f"生成 CVE {cve_info.cve_id} 的新总结")
        summary = await self.llm_service.generate_summary(cve_info)
        
        if summary:
            # 保存到缓存
            await self._save_summary_to_cache(summary, db)
            return summary
        
        return None
    
    async def _get_cached_summary(self, cve_id: str, db: AsyncSession) -> Optional[CVESummary]:
        """从缓存获取总结"""
        try:
            # 查询缓存
            stmt = select(CVESummaryCache).where(CVESummaryCache.cve_id == cve_id)
            result = await db.execute(stmt)
            cache_entry = result.scalar_one_or_none()
            
            if not cache_entry:
                return None
            
            # 检查是否过期
            if self._is_cache_expired(cache_entry.generated_at):
                print(f"CVE {cve_id} 的缓存已过期，删除缓存")
                await db.delete(cache_entry)
                await db.commit()
                return None
            
            # 返回缓存的总结
            return CVESummary(
                cve_id=cache_entry.cve_id,
                summary=cache_entry.summary,
                generated_at=cache_entry.generated_at,
                model_used=cache_entry.model_used
            )
            
        except Exception as e:
            print(f"获取缓存总结时出错: {e}")
            return None
    
    async def _save_summary_to_cache(self, summary: CVESummary, db: AsyncSession):
        """保存总结到缓存"""
        try:
            # 检查是否已存在
            stmt = select(CVESummaryCache).where(CVESummaryCache.cve_id == summary.cve_id)
            result = await db.execute(stmt)
            existing = result.scalar_one_or_none()
            
            if existing:
                # 更新现有记录
                existing.summary = summary.summary
                existing.generated_at = summary.generated_at
                existing.model_used = summary.model_used
            else:
                # 创建新记录
                cache_entry = CVESummaryCache(
                    cve_id=summary.cve_id,
                    summary=summary.summary,
                    generated_at=summary.generated_at,
                    model_used=summary.model_used
                )
                db.add(cache_entry)
            
            await db.commit()
            print(f"已保存 CVE {summary.cve_id} 的总结到缓存")
            
        except Exception as e:
            print(f"保存总结到缓存时出错: {e}")
            await db.rollback()
    
    def _is_cache_expired(self, generated_at: datetime) -> bool:
        """检查缓存是否过期"""
        expiry_time = generated_at + timedelta(seconds=self.cache_ttl)
        return datetime.utcnow() > expiry_time
    
    async def log_search(
        self, 
        query: str, 
        results_count: int, 
        search_time: float, 
        db: AsyncSession
    ):
        """记录搜索日志"""
        try:
            log_entry = SearchLog(
                id=str(uuid.uuid4()),
                query=query,
                results_count=str(results_count),
                search_time=search_time,
                timestamp=datetime.utcnow()
            )
            
            db.add(log_entry)
            await db.commit()
            
        except Exception as e:
            print(f"记录搜索日志时出错: {e}")
            await db.rollback()
    
    async def cleanup_expired_cache(self, db: AsyncSession):
        """清理过期的缓存"""
        try:
            expiry_time = datetime.utcnow() - timedelta(seconds=self.cache_ttl)
            
            stmt = delete(CVESummaryCache).where(CVESummaryCache.generated_at < expiry_time)
            result = await db.execute(stmt)
            
            deleted_count = result.rowcount
            await db.commit()
            
            if deleted_count > 0:
                print(f"清理了 {deleted_count} 个过期的缓存条目")
            
            return deleted_count
            
        except Exception as e:
            print(f"清理过期缓存时出错: {e}")
            await db.rollback()
            return 0
    
    async def get_cache_stats(self, db: AsyncSession) -> dict:
        """获取缓存统计信息"""
        try:
            # 总缓存数量
            total_stmt = select(CVESummaryCache)
            total_result = await db.execute(total_stmt)
            total_count = len(total_result.scalars().all())
            
            # 过期缓存数量
            expiry_time = datetime.utcnow() - timedelta(seconds=self.cache_ttl)
            expired_stmt = select(CVESummaryCache).where(CVESummaryCache.generated_at < expiry_time)
            expired_result = await db.execute(expired_stmt)
            expired_count = len(expired_result.scalars().all())
            
            return {
                "total_cached": total_count,
                "expired_cached": expired_count,
                "valid_cached": total_count - expired_count,
                "cache_ttl_hours": self.cache_ttl / 3600
            }
            
        except Exception as e:
            print(f"获取缓存统计时出错: {e}")
            return {
                "total_cached": 0,
                "expired_cached": 0,
                "valid_cached": 0,
                "cache_ttl_hours": self.cache_ttl / 3600
            }
