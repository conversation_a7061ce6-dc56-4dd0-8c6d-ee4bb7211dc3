<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVE 搜索服务</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <script src="https://unpkg.com/marked/marked.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .app-container {
            min-height: 100vh;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .search-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .search-section {
            padding: 40px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .search-input {
            margin-bottom: 20px;
        }

        .search-input .el-input__inner {
            font-size: 16px;
            padding: 15px 20px;
            border-radius: 12px;
        }

        .search-input .el-input-group__prepend,
        .search-input .el-input-group__append {
            border-radius: 12px;
        }
        
        .search-options {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            background: rgba(255, 255, 255, 0.8);
            padding: 15px 20px;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
        }

        .results-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .results-selector .el-select {
            width: 100px;
        }
        
        .examples-section {
            padding: 30px 40px;
            border-top: 1px solid #eee;
        }
        
        .example-item {
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #409eff;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .example-item:hover {
            background: #e3f2fd;
            transform: translateX(5px);
        }
        
        .example-title {
            font-weight: bold;
            color: #409eff;
            margin-bottom: 5px;
        }
        
        .example-query {
            font-family: 'Courier New', monospace;
            background: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
            border: 1px solid #ddd;
        }
        
        .results-section {
            padding: 40px;
        }
        
        .result-item {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #eee;
            border-radius: 12px;
            background: #fafafa;
            transition: all 0.3s;
        }
        
        .result-item:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .cve-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .cve-id {
            font-size: 1.3rem;
            font-weight: bold;
            color: #e74c3c;
        }
        
        .cve-date {
            color: #666;
            font-size: 0.9rem;
        }
        
        .cve-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .cve-description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .cve-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .cve-summary {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .summary-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            font-weight: bold;
            color: #2e7d32;
        }

        .summary-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .summary-content {
            line-height: 1.6;
        }

        .summary-content h1,
        .summary-content h2,
        .summary-content h3 {
            margin: 10px 0 5px 0;
            color: #2e7d32;
        }

        .summary-content p {
            margin: 8px 0;
        }

        .summary-content ul,
        .summary-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .summary-content code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        .summary-content blockquote {
            border-left: 4px solid #4caf50;
            margin: 10px 0;
            padding-left: 15px;
            color: #666;
        }

        .summary-placeholder {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
            color: #666;
        }
        
        .loading-container {
            text-align: center;
            padding: 40px;
        }
        
        .stats-info {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .search-section, .results-section {
                padding: 20px;
            }
            
            .search-options {
                flex-direction: column;
                align-items: stretch;
            }
            
            .cve-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .cve-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 头部 -->
            <div class="header">
                <h1>🔍 CVE 搜索服务</h1>
                <p>基于 AI 的漏洞信息搜索与总结平台</p>
            </div>
            
            <!-- 搜索容器 -->
            <div class="search-container">
                <!-- 搜索区域 -->
                <div class="search-section">
                    <el-input
                        v-model="searchQuery"
                        placeholder="输入搜索条件，例如：remote code execution"
                        size="large"
                        class="search-input"
                        @keyup.enter="performSearch"
                        clearable
                    >
                        <template #prepend>
                            <el-icon><Search /></el-icon>
                        </template>
                        <template #append>
                            <el-button
                                type="primary"
                                @click="performSearch"
                                :loading="searching"
                                size="large"
                            >
                                搜索
                            </el-button>
                        </template>
                    </el-input>

                    <div class="search-options">
                        <div class="results-selector">
                            <span>最大结果数：</span>
                            <el-select v-model="maxResults" size="small">
                                <el-option label="10" :value="10"></el-option>
                                <el-option label="20" :value="20"></el-option>
                                <el-option label="50" :value="50"></el-option>
                                <el-option label="100" :value="100"></el-option>
                            </el-select>
                        </div>

                        <el-button
                            size="small"
                            @click="showExamples = !showExamples"
                            :type="showExamples ? 'primary' : 'default'"
                        >
                            {{ showExamples ? '隐藏' : '显示' }}搜索示例
                        </el-button>
                    </div>
                </div>
                
                <!-- 搜索示例 -->
                <div v-if="showExamples" class="examples-section">
                    <h3>🎯 搜索语法示例</h3>
                    <div 
                        v-for="example in examples" 
                        :key="example.title"
                        class="example-item"
                        @click="useExample(example.query)"
                    >
                        <div class="example-title">{{ example.title }}</div>
                        <div class="example-query">{{ example.query }}</div>
                        <div style="color: #666; font-size: 0.9rem;">{{ example.description }}</div>
                    </div>
                </div>
                
                <!-- 搜索结果 -->
                <div v-if="hasSearched" class="results-section">
                    <!-- 加载状态 -->
                    <div v-if="searching" class="loading-container">
                        <el-icon class="is-loading" size="40"><Loading /></el-icon>
                        <p>正在搜索中...</p>
                    </div>
                    
                    <!-- 搜索统计 -->
                    <div v-else-if="searchResults" class="stats-info">
                        <el-row justify="space-between" align="middle">
                            <el-col :span="12">
                                <el-tag type="success" size="large">
                                    <el-icon><DocumentChecked /></el-icon>
                                    找到 {{ searchResults.total_results }} 个 CVE 结果
                                </el-tag>
                            </el-col>
                            <el-col :span="12" style="text-align: right;">
                                <el-tag type="info">
                                    搜索耗时: {{ searchResults.search_time.toFixed(2) }} 秒
                                </el-tag>
                            </el-col>
                        </el-row>
                    </div>
                    
                    <!-- 结果列表 -->
                    <div v-if="searchResults && searchResults.results.length > 0">
                        <div 
                            v-for="result in searchResults.results" 
                            :key="result.cve_info.cve_id"
                            class="result-item"
                        >
                            <!-- CVE 头部信息 -->
                            <div class="cve-header">
                                <span class="cve-id">{{ result.cve_info.cve_id }}</span>
                                <span class="cve-date">{{ formatDate(result.cve_info.published_date) }}</span>
                            </div>
                            
                            <!-- CVE 标题 -->
                            <div v-if="result.cve_info.title" class="cve-title">
                                {{ result.cve_info.title }}
                            </div>
                            
                            <!-- CVE 描述 -->
                            <div v-if="result.cve_info.description" class="cve-description">
                                {{ truncateText(result.cve_info.description, 300) }}
                            </div>
                            
                            <!-- CVE 元信息 -->
                            <div class="cve-meta">
                                <div v-if="result.cve_info.severity" class="meta-item">
                                    <el-tag :type="getSeverityType(result.cve_info.severity)">
                                        {{ result.cve_info.severity }}
                                    </el-tag>
                                </div>
                                
                                <div v-if="result.cve_info.cvss_score" class="meta-item">
                                    <el-tag type="warning">
                                        CVSS: {{ result.cve_info.cvss_score }}
                                    </el-tag>
                                </div>
                                
                                <div v-if="result.cve_info.affected_products.length > 0" class="meta-item">
                                    <el-tag type="info">
                                        影响产品: {{ result.cve_info.affected_products.slice(0, 3).join(', ') }}
                                        <span v-if="result.cve_info.affected_products.length > 3">...</span>
                                    </el-tag>
                                </div>
                            </div>
                            
                            <!-- AI 总结区域 -->
                            <div v-if="result.summary" class="cve-summary">
                                <div class="summary-header">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <el-icon><ChatDotRound /></el-icon>
                                        AI 总结
                                        <el-tag size="small" type="success">{{ result.summary.model_used }}</el-tag>
                                    </div>
                                    <div class="summary-actions">
                                        <el-button
                                            size="small"
                                            type="primary"
                                            @click="refreshSummary(result.cve_info.cve_id)"
                                            :loading="loadingSummaries[result.cve_info.cve_id]"
                                        >
                                            刷新总结
                                        </el-button>
                                    </div>
                                </div>
                                <div class="summary-content" v-html="renderMarkdown(result.summary.summary)"></div>
                            </div>

                            <!-- 无总结时显示生成按钮 -->
                            <div v-else class="summary-placeholder">
                                <p>暂无 AI 总结</p>
                                <el-button
                                    type="primary"
                                    @click="generateSummary(result.cve_info.cve_id)"
                                    :loading="loadingSummaries[result.cve_info.cve_id]"
                                >
                                    <el-icon><ChatDotRound /></el-icon>
                                    生成 AI 总结
                                </el-button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 无结果 -->
                    <div v-else-if="searchResults && searchResults.results.length === 0">
                        <el-empty description="未找到匹配的 CVE">
                            <el-button type="primary" @click="showExamples = true">查看搜索示例</el-button>
                        </el-empty>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElMessage, ElNotification } = ElementPlus;

        createApp({
            setup() {
                // 响应式数据
                const searchQuery = ref('');
                const maxResults = ref(50);
                const searching = ref(false);
                const hasSearched = ref(false);
                const searchResults = ref(null);
                const showExamples = ref(true);
                const examples = ref([]);
                const loadingSummaries = ref({});

                // 加载搜索示例
                const loadExamples = async () => {
                    try {
                        const response = await fetch('/api/examples');
                        const data = await response.json();
                        examples.value = data.examples;
                    } catch (error) {
                        console.error('加载示例失败:', error);
                    }
                };

                // 执行搜索
                const performSearch = async () => {
                    if (!searchQuery.value.trim()) {
                        ElMessage.warning('请输入搜索条件');
                        return;
                    }

                    searching.value = true;
                    hasSearched.value = true;
                    searchResults.value = null;

                    // 搜索后自动收起示例
                    showExamples.value = false;

                    try {
                        const params = new URLSearchParams({
                            query: searchQuery.value,
                            max_results: maxResults.value
                        });

                        const response = await fetch(`/api/search?${params}`);

                        if (!response.ok) {
                            throw new Error(`搜索失败: ${response.statusText}`);
                        }

                        const data = await response.json();
                        searchResults.value = data;

                        ElNotification({
                            title: '搜索完成',
                            message: `找到 ${data.total_results} 个结果`,
                            type: 'success',
                            duration: 3000
                        });

                    } catch (error) {
                        console.error('搜索错误:', error);
                        ElMessage.error(`搜索失败: ${error.message}`);
                    } finally {
                        searching.value = false;
                    }
                };

                // 使用示例查询
                const useExample = (query) => {
                    searchQuery.value = query;
                    showExamples.value = false;
                };

                // 生成 AI 总结
                const generateSummary = async (cveId) => {
                    loadingSummaries.value[cveId] = true;

                    try {
                        const response = await fetch(`/api/summary/${cveId}`);

                        if (!response.ok) {
                            throw new Error(`生成总结失败: ${response.statusText}`);
                        }

                        const summary = await response.json();

                        // 更新搜索结果中的总结
                        if (searchResults.value && searchResults.value.results) {
                            const result = searchResults.value.results.find(r => r.cve_info.cve_id === cveId);
                            if (result) {
                                result.summary = summary;
                            }
                        }

                        ElMessage.success('AI 总结生成成功');

                    } catch (error) {
                        console.error('生成总结错误:', error);
                        ElMessage.error(`生成总结失败: ${error.message}`);
                    } finally {
                        loadingSummaries.value[cveId] = false;
                    }
                };

                // 刷新 AI 总结
                const refreshSummary = async (cveId) => {
                    loadingSummaries.value[cveId] = true;

                    try {
                        const response = await fetch(`/api/summary/${cveId}?force_refresh=true`);

                        if (!response.ok) {
                            throw new Error(`刷新总结失败: ${response.statusText}`);
                        }

                        const summary = await response.json();

                        // 更新搜索结果中的总结
                        if (searchResults.value && searchResults.value.results) {
                            const result = searchResults.value.results.find(r => r.cve_info.cve_id === cveId);
                            if (result) {
                                result.summary = summary;
                            }
                        }

                        ElMessage.success('AI 总结已刷新');

                    } catch (error) {
                        console.error('刷新总结错误:', error);
                        ElMessage.error(`刷新总结失败: ${error.message}`);
                    } finally {
                        loadingSummaries.value[cveId] = false;
                    }
                };

                // 渲染 Markdown
                const renderMarkdown = (text) => {
                    if (!text) return '';
                    try {
                        return marked.parse(text);
                    } catch (error) {
                        console.error('Markdown 渲染错误:', error);
                        return text;
                    }
                };

                // 格式化日期
                const formatDate = (dateStr) => {
                    if (!dateStr) return '未知';
                    try {
                        return new Date(dateStr).toLocaleDateString('zh-CN');
                    } catch {
                        return dateStr;
                    }
                };

                // 截断文本
                const truncateText = (text, maxLength) => {
                    if (!text) return '';
                    if (text.length <= maxLength) return text;
                    return text.substring(0, maxLength) + '...';
                };

                // 获取严重程度类型
                const getSeverityType = (severity) => {
                    const severityMap = {
                        'CRITICAL': 'danger',
                        'HIGH': 'danger',
                        'MEDIUM': 'warning',
                        'LOW': 'info'
                    };
                    return severityMap[severity?.toUpperCase()] || 'info';
                };

                // 组件挂载时加载示例
                onMounted(() => {
                    loadExamples();
                    // 没有搜索时显示示例
                    showExamples.value = !hasSearched.value;
                });

                return {
                    searchQuery,
                    maxResults,
                    searching,
                    hasSearched,
                    searchResults,
                    showExamples,
                    examples,
                    loadingSummaries,
                    performSearch,
                    useExample,
                    generateSummary,
                    refreshSummary,
                    renderMarkdown,
                    formatDate,
                    truncateText,
                    getSeverityType
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
