<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVE 搜索服务</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <script src="https://unpkg.com/marked/marked.min.js"></script>
    <script src="https://unpkg.com/dompurify@2.4.7/dist/purify.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Aria<PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .app-container {
            min-height: 100vh;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .search-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .search-section {
            padding: 40px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .search-input {
            margin-bottom: 20px;
        }

        .search-input .el-input__inner {
            font-size: 16px;
            padding: 15px 20px;
            border-radius: 12px;
        }

        .search-input .el-input-group__prepend,
        .search-input .el-input-group__append {
            border-radius: 12px;
        }
        
        .search-options {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
            margin-bottom: 20px;
        }

        .options-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .results-selector {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .page-size-buttons {
            display: flex;
            gap: 5px;
        }

        .examples-toggle {
            display: flex;
            align-items: center;
        }
        
        .examples-section {
            padding: 30px 40px;
            border-top: 1px solid #eee;
        }
        
        .example-item {
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #409eff;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .example-item:hover {
            background: #e3f2fd;
            transform: translateX(5px);
        }
        
        .example-title {
            font-weight: bold;
            color: #409eff;
            margin-bottom: 5px;
        }
        
        .example-query {
            font-family: 'Courier New', monospace;
            background: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
            border: 1px solid #ddd;
        }
        
        .results-section {
            padding: 40px;
        }
        
        .result-item {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #eee;
            border-radius: 12px;
            background: #fafafa;
            transition: all 0.3s;
        }
        
        .result-item:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .cve-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .cve-id {
            font-size: 1.3rem;
            font-weight: bold;
            color: #e74c3c;
        }
        
        .cve-date {
            color: #666;
            font-size: 0.9rem;
        }
        
        .cve-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .cve-description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .cve-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .cve-summary {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .summary-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            font-weight: bold;
            color: #2e7d32;
        }

        .summary-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .summary-content {
            line-height: 1.6;
        }

        .summary-content h1,
        .summary-content h2,
        .summary-content h3 {
            margin: 10px 0 5px 0;
            color: #2e7d32;
        }

        .summary-content p {
            margin: 8px 0;
        }

        .summary-content ul,
        .summary-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .summary-content code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        .summary-content blockquote {
            border-left: 4px solid #4caf50;
            margin: 10px 0;
            padding-left: 15px;
            color: #666;
        }

        .summary-placeholder {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
            color: #666;
        }
        
        .loading-container {
            text-align: center;
            padding: 40px;
        }
        
        .stats-info {
            margin-bottom: 20px;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .back-to-top:hover {
            background: #337ecc;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
        }

        .back-to-top.hidden {
            opacity: 0;
            visibility: hidden;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .search-section, .results-section {
                padding: 20px;
            }
            
            .search-options {
                flex-direction: column;
                align-items: stretch;
            }
            
            .cve-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .cve-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 头部 -->
            <div class="header">
                <h1>🔍 CVE 搜索服务</h1>
                <p>基于 AI 的漏洞信息搜索与总结平台</p>
            </div>
            
            <!-- 搜索容器 -->
            <div class="search-container">
                <!-- 搜索区域 -->
                <div class="search-section">
                    <el-input
                        v-model="searchQuery"
                        placeholder="输入搜索条件，例如：remote code execution"
                        size="large"
                        class="search-input"
                        @keyup.enter="performSearch"
                        clearable
                    >
                        <template #prepend>
                            <el-icon><Search /></el-icon>
                        </template>
                        <template #append>
                            <el-button
                                type="primary"
                                @click="performSearch"
                                :loading="searching"
                                size="large"
                            >
                                搜索
                            </el-button>
                        </template>
                    </el-input>

                    <div class="search-options">
                        <div class="options-row">
                            <div class="results-selector">
                                <span>每页显示：</span>
                                <el-button-group class="page-size-buttons">
                                    <el-button
                                        v-for="size in pageSizeOptions"
                                        :key="size"
                                        :type="pageSize === size ? 'primary' : 'default'"
                                        size="small"
                                        @click="changePageSize(size)"
                                    >
                                        {{ size }}
                                    </el-button>
                                </el-button-group>
                            </div>

                            <div class="examples-toggle">
                                <el-button
                                    size="small"
                                    @click="toggleExamples"
                                    :type="showExamples ? 'primary' : 'default'"
                                    :icon="showExamples ? 'ArrowUp' : 'ArrowDown'"
                                >
                                    {{ showExamples ? '隐藏' : '显示' }}搜索示例
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 搜索示例 -->
                <div v-if="showExamples" class="examples-section">
                    <h3>🎯 搜索语法示例</h3>
                    <div 
                        v-for="example in examples" 
                        :key="example.title"
                        class="example-item"
                        @click="useExample(example.query)"
                    >
                        <div class="example-title">{{ example.title }}</div>
                        <div class="example-query">{{ example.query }}</div>
                        <div style="color: #666; font-size: 0.9rem;">{{ example.description }}</div>
                    </div>
                </div>
                
                <!-- 搜索结果 -->
                <div v-if="hasSearched" class="results-section">
                    <!-- 加载状态 -->
                    <div v-if="searching" class="loading-container">
                        <el-icon class="is-loading" size="40"><Loading /></el-icon>
                        <p>正在搜索中...</p>
                    </div>
                    
                    <!-- 搜索统计 -->
                    <div v-else-if="searchResults" class="stats-info">
                        <el-row justify="space-between" align="middle">
                            <el-col :span="16">
                                <el-space>
                                    <el-tag type="success" size="large">
                                        <el-icon><DocumentChecked /></el-icon>
                                        共找到 {{ searchResults.total_results }} 个 CVE
                                    </el-tag>
                                    <el-tag type="info">
                                        第 {{ searchResults.page }} / {{ searchResults.total_pages }} 页
                                    </el-tag>
                                    <el-tag type="warning">
                                        显示 {{ getDisplayRange() }}
                                    </el-tag>
                                </el-space>
                            </el-col>
                            <el-col :span="8" style="text-align: right;">
                                <el-tag type="info">
                                    搜索耗时: {{ searchResults.search_time.toFixed(2) }} 秒
                                </el-tag>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 顶部分页 -->
                    <div v-if="searchResults && searchResults.total_pages > 1" class="pagination-container">
                        <el-pagination
                            v-model:current-page="currentPage"
                            :page-size="pageSize"
                            :total="searchResults.total_results"
                            :page-count="searchResults.total_pages"
                            layout="prev, pager, next, jumper"
                            @current-change="handlePageChange"
                            background
                        />
                    </div>
                    
                    <!-- 结果列表 -->
                    <div v-if="searchResults && searchResults.results.length > 0">
                        <div 
                            v-for="result in searchResults.results" 
                            :key="result.cve_info.cve_id"
                            class="result-item"
                        >
                            <!-- CVE 头部信息 -->
                            <div class="cve-header">
                                <span class="cve-id">{{ result.cve_info.cve_id }}</span>
                                <span class="cve-date">{{ formatDate(result.cve_info.published_date) }}</span>
                            </div>
                            
                            <!-- CVE 标题 -->
                            <div v-if="result.cve_info.title" class="cve-title">
                                {{ result.cve_info.title }}
                            </div>
                            
                            <!-- CVE 描述 -->
                            <div v-if="result.cve_info.description" class="cve-description">
                                {{ truncateText(result.cve_info.description, 300) }}
                            </div>
                            
                            <!-- CVE 元信息 -->
                            <div class="cve-meta">
                                <div v-if="result.cve_info.severity" class="meta-item">
                                    <el-tag :type="getSeverityType(result.cve_info.severity)">
                                        {{ result.cve_info.severity }}
                                    </el-tag>
                                </div>
                                
                                <div v-if="result.cve_info.cvss_score" class="meta-item">
                                    <el-tag type="warning">
                                        CVSS: {{ result.cve_info.cvss_score }}
                                    </el-tag>
                                </div>
                                
                                <div v-if="result.cve_info.affected_products.length > 0" class="meta-item">
                                    <el-tag type="info">
                                        影响产品: {{ result.cve_info.affected_products.slice(0, 3).join(', ') }}
                                        <span v-if="result.cve_info.affected_products.length > 3">...</span>
                                    </el-tag>
                                </div>
                            </div>
                            
                            <!-- AI 总结区域 -->
                            <div v-if="result.summary" class="cve-summary">
                                <div class="summary-header">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <el-icon><ChatDotRound /></el-icon>
                                        AI 总结
                                        <el-tag size="small" type="success">{{ result.summary.model_used }}</el-tag>
                                    </div>
                                    <div class="summary-actions">
                                        <el-button
                                            size="small"
                                            type="primary"
                                            @click="refreshSummary(result.cve_info.cve_id)"
                                            :loading="loadingSummaries[result.cve_info.cve_id]"
                                        >
                                            刷新总结
                                        </el-button>
                                    </div>
                                </div>
                                <div class="summary-content" v-html="renderMarkdown(result.summary.summary)"></div>
                            </div>

                            <!-- 无总结时显示生成按钮 -->
                            <div v-else class="summary-placeholder">
                                <p>暂无 AI 总结</p>
                                <el-button
                                    type="primary"
                                    @click="generateSummary(result.cve_info.cve_id)"
                                    :loading="loadingSummaries[result.cve_info.cve_id]"
                                >
                                    <el-icon><ChatDotRound /></el-icon>
                                    生成 AI 总结
                                </el-button>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <div v-if="searchResults.total_pages > 1" class="pagination-container">
                            <el-pagination
                                v-model:current-page="currentPage"
                                :page-size="pageSize"
                                :total="searchResults.total_results"
                                :page-count="searchResults.total_pages"
                                layout="prev, pager, next, jumper"
                                @current-change="handlePageChange"
                                background
                            />
                        </div>
                    </div>

                    <!-- 无结果 -->
                    <div v-else-if="searchResults && searchResults.results.length === 0">
                        <el-empty description="未找到匹配的 CVE">
                            <el-button type="primary" @click="showExamples = true">查看搜索示例</el-button>
                        </el-empty>
                    </div>
                </div>
            </div>
        </div>

        <!-- 回到顶部按钮 -->
        <button
            class="back-to-top"
            :class="{ hidden: !showBackToTop }"
            @click="scrollToTop"
            title="回到顶部"
        >
            <el-icon><ArrowUp /></el-icon>
        </button>
    </div>

    <script>
        const { createApp, ref, onMounted, onUnmounted } = Vue;
        const { ElMessage, ElNotification } = ElementPlus;

        createApp({
            setup() {
                // 响应式数据
                const searchQuery = ref('');
                const pageSize = ref(50);
                const currentPage = ref(1);
                const pageSizeOptions = ref([10, 20, 50, 100]);
                const searching = ref(false);
                const hasSearched = ref(false);
                const searchResults = ref(null);
                const showExamples = ref(true);
                const examples = ref([]);
                const loadingSummaries = ref({});
                const showBackToTop = ref(false);

                // 加载搜索示例
                const loadExamples = async () => {
                    try {
                        const response = await fetch('/api/examples');
                        const data = await response.json();
                        examples.value = data.examples;
                    } catch (error) {
                        console.error('加载示例失败:', error);
                    }
                };

                // 执行搜索
                const performSearch = async (resetPage = true) => {
                    if (!searchQuery.value.trim()) {
                        ElMessage.warning('请输入搜索条件');
                        return;
                    }

                    if (resetPage) {
                        currentPage.value = 1;
                    }

                    searching.value = true;
                    hasSearched.value = true;

                    // 搜索后自动收起示例
                    if (resetPage) {
                        showExamples.value = false;
                    }

                    try {
                        const params = new URLSearchParams({
                            query: searchQuery.value,
                            page: currentPage.value,
                            page_size: pageSize.value
                        });

                        const response = await fetch(`/api/search?${params}`);

                        if (!response.ok) {
                            throw new Error(`搜索失败: ${response.statusText}`);
                        }

                        const data = await response.json();
                        searchResults.value = data;

                        if (resetPage) {
                            ElNotification({
                                title: '搜索完成',
                                message: `找到 ${data.total_results} 个结果`,
                                type: 'success',
                                duration: 3000
                            });
                        }

                    } catch (error) {
                        console.error('搜索错误:', error);
                        ElMessage.error(`搜索失败: ${error.message}`);
                    } finally {
                        searching.value = false;
                    }
                };

                // 使用示例查询
                const useExample = (query) => {
                    searchQuery.value = query;
                    showExamples.value = false;
                };

                // 切换示例显示
                const toggleExamples = () => {
                    showExamples.value = !showExamples.value;
                };

                // 改变页面大小
                const changePageSize = (newSize) => {
                    pageSize.value = newSize;
                    currentPage.value = 1;
                    if (hasSearched.value && searchQuery.value.trim()) {
                        performSearch(false);
                    }
                };

                // 处理页面变化
                const handlePageChange = (page) => {
                    currentPage.value = page;
                    performSearch(false);
                    // 滚动到顶部
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                };

                // 获取显示范围
                const getDisplayRange = () => {
                    if (!searchResults.value) return '';
                    const start = (searchResults.value.page - 1) * searchResults.value.page_size + 1;
                    const end = Math.min(start + searchResults.value.results.length - 1, searchResults.value.total_results);
                    return `${start}-${end}`;
                };

                // 回到顶部
                const scrollToTop = () => {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                };

                // 监听滚动事件
                const handleScroll = () => {
                    showBackToTop.value = window.scrollY > 300;
                };

                // 生成 AI 总结
                const generateSummary = async (cveId) => {
                    loadingSummaries.value[cveId] = true;

                    try {
                        const response = await fetch(`/api/summary/${cveId}`);

                        if (!response.ok) {
                            throw new Error(`生成总结失败: ${response.statusText}`);
                        }

                        const summary = await response.json();

                        // 更新搜索结果中的总结
                        if (searchResults.value && searchResults.value.results) {
                            const result = searchResults.value.results.find(r => r.cve_info.cve_id === cveId);
                            if (result) {
                                result.summary = summary;
                            }
                        }

                        ElMessage.success('AI 总结生成成功');

                    } catch (error) {
                        console.error('生成总结错误:', error);
                        ElMessage.error(`生成总结失败: ${error.message}`);
                    } finally {
                        loadingSummaries.value[cveId] = false;
                    }
                };

                // 刷新 AI 总结
                const refreshSummary = async (cveId) => {
                    loadingSummaries.value[cveId] = true;

                    try {
                        const response = await fetch(`/api/summary/${cveId}?force_refresh=true`);

                        if (!response.ok) {
                            throw new Error(`刷新总结失败: ${response.statusText}`);
                        }

                        const summary = await response.json();

                        // 更新搜索结果中的总结
                        if (searchResults.value && searchResults.value.results) {
                            const result = searchResults.value.results.find(r => r.cve_info.cve_id === cveId);
                            if (result) {
                                result.summary = summary;
                            }
                        }

                        ElMessage.success('AI 总结已刷新');

                    } catch (error) {
                        console.error('刷新总结错误:', error);
                        ElMessage.error(`刷新总结失败: ${error.message}`);
                    } finally {
                        loadingSummaries.value[cveId] = false;
                    }
                };

                // 渲染 Markdown
                const renderMarkdown = (text) => {
                    if (!text) return '';
                    try {
                        // 配置 marked 选项
                        marked.setOptions({
                            breaks: true,
                            gfm: true
                        });

                        const html = marked.parse(text);
                        // 使用 DOMPurify 清理 HTML
                        return DOMPurify.sanitize(html);
                    } catch (error) {
                        console.error('Markdown 渲染错误:', error);
                        // 如果渲染失败，返回纯文本
                        return text.replace(/\n/g, '<br>');
                    }
                };

                // 格式化日期
                const formatDate = (dateStr) => {
                    if (!dateStr) return '未知';
                    try {
                        return new Date(dateStr).toLocaleDateString('zh-CN');
                    } catch {
                        return dateStr;
                    }
                };

                // 截断文本
                const truncateText = (text, maxLength) => {
                    if (!text) return '';
                    if (text.length <= maxLength) return text;
                    return text.substring(0, maxLength) + '...';
                };

                // 获取严重程度类型
                const getSeverityType = (severity) => {
                    const severityMap = {
                        'CRITICAL': 'danger',
                        'HIGH': 'danger',
                        'MEDIUM': 'warning',
                        'LOW': 'info'
                    };
                    return severityMap[severity?.toUpperCase()] || 'info';
                };

                // 组件挂载时加载示例
                onMounted(() => {
                    loadExamples();
                    // 没有搜索时显示示例
                    showExamples.value = !hasSearched.value;
                    // 添加滚动监听
                    window.addEventListener('scroll', handleScroll);
                });

                // 组件卸载时清理监听器
                onUnmounted(() => {
                    window.removeEventListener('scroll', handleScroll);
                });

                return {
                    searchQuery,
                    pageSize,
                    currentPage,
                    pageSizeOptions,
                    searching,
                    hasSearched,
                    searchResults,
                    showExamples,
                    examples,
                    loadingSummaries,
                    showBackToTop,
                    performSearch,
                    useExample,
                    toggleExamples,
                    changePageSize,
                    handlePageChange,
                    getDisplayRange,
                    generateSummary,
                    refreshSummary,
                    renderMarkdown,
                    scrollToTop,
                    formatDate,
                    truncateText,
                    getSeverityType
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
